* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body, html {
    font-family: 'Poppins', sans-serif;
    scroll-behavior: smooth;
    background-color: #ffffff;
    color: #333;
  }

  a {
    text-decoration: none;
    color: inherit;
  }

  ul {
    list-style: none;
  }

.hero {
  position: relative;
  height: 100vh;
  background-image: url('/haris/images/bg2.jpg'); 
  background-size: cover;
  background-position: center;
  background-attachment: fixed; /* Sticky effect */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #0f0b0b;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(10, 10, 10, 0.5); /* Dark overlay for readability */
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 1;
  padding: 20px;
  animation: fadeInHero 1s ease;
}

.hero-content h1 {
  font-size: 3.5rem;
  font-weight: 700;
  letter-spacing: 2px;
  text-shadow: 2px 4px 12px rgba(0, 0, 0, 0.4);
}

/* Responsive Text */
@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 2.2rem;
  }
}

/* Fade-in Animation */
@keyframes fadeInHero {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


  /* Sticky nav */
  nav {
    position: sticky;
    top: 0;
    background: #ffffffee;
    display: flex;
    justify-content: center;
    gap: 2rem;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    z-index: 100;
  }

  nav a {
    font-weight: 600;
    color: #444;
    padding: 0.5rem;
    transition: color 0.3s;
  }

  nav a:hover {
    color: #6c5ce7;
  }

  section {
    padding: 5rem 2rem;
    max-width: 1000px;
    margin: auto;
  }

  section h2 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #6c5ce7;
  }

  section p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #444;
  }

  .portfolio-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
  }

  .portfolio-grid img {
    width: 100%;
    border-radius: 12px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  }

  .portfolio-grid img:hover {
    transform: scale(1.03);
    box-shadow: 0 8px 18px rgba(0,0,0,0.15);
  }

  .contact-form input,
  .contact-form textarea {
    width: 100%;
    padding: 0.8rem;
    margin-bottom: 1rem;
    border: 1px solid #ccc;
    border-radius: 10px;
    font-size: 1rem;
  }

  .contact-form button {
    background: #6c5ce7;
    color: white;
    padding: 0.75rem 2rem;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    font-size: 1rem;
    transition: background 0.3s ease;
  }

  .contact-form button:hover {
    background: #5a4bd1;
  }

  footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 1rem;
  }

  @media (max-width: 600px) {
    .hero h1 {
      font-size: 2rem;
    }

    nav {
      flex-wrap: wrap;
      gap: 1rem;
    }
  }

  .whatsapp-btn {
    width: 40px;
    height: 40px;
    display: inline-block;
    background-color: #25D366;
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  }

  .whatsapp-btn svg {
    width: 20px;
    height: 20px;
    vertical-align: middle;
    fill: white;
  }

/* General Page Style (optional for full section) */
body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background: #f0f2f5;
  color: #333;
  margin: 0;
  padding: 0;
}

/* Section Heading */
h2 {
  text-align: center;
  font-size: 2.5rem;
  margin-top: 40px;
  color: #222;
  position: relative;
}

h2::after {
  content: '';
  display: block;
  width: 60px;
  height: 4px;
  background-color: #4CAF50;
  margin: 10px auto 0;
  border-radius: 2px;
}

/* Skills Grid */
.skills {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 30px;
  padding: 40px 20px;
  max-width: 900px;
  margin: 0 auto;
}

/* Each Skill Box */
.skill {
  width: 100%;
  height: 180px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
  background: #fff;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

/* Image Style */
.skill img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  border-radius: 16px;
}

/* Hover Effect */
.skill:hover {
  transform: scale(1.07);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

/* Section Styles */
#services {
  background: linear-gradient(135deg, #f7f9fb, #e6ecf0);
  padding: 60px 20px;
  text-align: center;
}

/* Heading */
#services h2 {
  font-size: 2.8rem;
  margin-bottom: 30px;
  color: #2c3e50;
  position: relative;
}

#services h2::after {
  content: '';
  display: block;
  width: 70px;
  height: 4px;
  background-color: #4CAF50;
  margin: 10px auto 0;
  border-radius: 2px;
}

/* Services List */
#services ul {
  list-style: none;
  padding: 0;
  max-width: 700px;
  margin: 0 auto;
}

#services li {
  font-size: 1.2rem;
  margin: 15px 0;
  padding: 15px 20px;
  background: white;
  border-left: 5px solid #4CAF50;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-align: left;
}

#services li:hover {
  transform: translateX(5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}


/* Contact Section Styling */
#contact {
  padding: 80px 20px;
  background: linear-gradient(135deg, #e0f7ec, #ffffff);
  text-align: center;
  box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  margin: 40px auto;
  max-width: 700px;
}

#contact h2 {
  font-size: 2.8rem;
  margin-bottom: 40px;
  color: #0abf53;
  letter-spacing: 1px;
  position: relative;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 25px;
  align-items: center;
}

/* WhatsApp Button */
.whatsapp-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #25D366;
  padding: 14px 20px;
  border-radius: 50px;
  text-decoration: none;
  transition: 0.3s ease;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
}

.whatsapp-btn img {
  width: 32px;
  height: 32px;
}

.whatsapp-btn:hover {
  transform: scale(1.05);
  background-color: #1da851;
}

/* Contact Items */
.contact-info div {
  font-size: 1.1rem;
  color: #444;
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #ffffff;
  padding: 12px 20px;
  border-radius: 12px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transition: 0.3s ease;
}

.contact-info div:hover {
  transform: translateY(-3px);
  background-color: #f8fff9;
}

.contact-info a {
  color: #0abf53;
  text-decoration: none;
  font-weight: 500;
}

.icon {
  font-size: 1.3rem;
}

/* Responsive */
@media (max-width: 600px) {
  #contact {
    padding: 50px 15px;
  }

  #contact h2 {
    font-size: 2rem;
  }

  .contact-info div {
    font-size: 1rem;
  }

  .whatsapp-btn img {
    width: 28px;
    height: 28px;
  }
}
